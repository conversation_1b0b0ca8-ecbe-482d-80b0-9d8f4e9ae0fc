import pandas as pd
import re
from collections import defaultdict

# 验证函数：检查特定艾特者和被艾特者的关系
def verify_mention_count(author, mentioned_user):
    """
    验证特定艾特者和被艾特者的共现次数
    """
    print(f"\n=== 验证 '{author}' @'{mentioned_user}' 的共现次数 ===")

    # 读取原始数据
    df = pd.read_csv('1.csv', encoding='gb18030')

    # 筛选出该作者的所有记录
    author_records = df[df['原文作者'] == author]
    print(f"作者 '{author}' 总共有 {len(author_records)} 条记录")

    # 查找包含目标@用户的记录
    matching_records = author_records[author_records['全文内容'].str.contains(f'@{mentioned_user}', na=False)]
    print(f"包含 '@{mentioned_user}' 的记录有 {len(matching_records)} 条")

    # 详细分析每条记录（修改为去重逻辑）
    count = 0
    for idx, row in matching_records.iterrows():
        content = row['全文内容']
        mentions = extract_at_mentions(content)
        unique_mentions = list(set(mentions))  # 去重
        if mentioned_user in unique_mentions:
            count += 1  # 每条记录最多计算1次
            target_count = mentions.count(mentioned_user)
            print(f"第{idx}行: 原本有 {target_count} 次 '@{mentioned_user}'，去重后计算为 1 次")
            print(f"  内容: {content[:100]}...")
            print(f"  提取到的@用户: {mentions}")
            print("---")

    print(f"总计算出的共现次数: {count}")
    return count

def extract_at_mentions(text):
    """
    从文本中提取@后面的用户名
    规则：
    1. 提取@后面的文本，只保留中文、英文、数字、下划线
    2. 如果@后面有冒号，则不提取该@
    3. 返回所有提取到的用户名列表
    """
    if pd.isna(text):
        return []

    # 查找所有@符号的位置
    mentions = []

    # 使用正则表达式查找@后面的用户名
    # @后面跟着的是中文、英文、数字、下划线的组合，直到遇到非这些字符为止
    pattern = r'@([a-zA-Z0-9_\u4e00-\u9fa5]+)'

    matches = re.finditer(pattern, text)

    for match in matches:
        mention_text = match.group(1)

        # 检查@后面是否紧跟着冒号，如果是则跳过
        after_mention = text[match.end():]
        if after_mention.startswith(':'):
            continue

        mentions.append(mention_text)

    return mentions

def create_mention_network():
    """
    创建@关系网络表
    """
    # 读取数据
    print("正在读取数据...")
    df = pd.read_csv('1.csv', encoding='gb18030')

    # 用于统计共现次数的字典
    mention_count = defaultdict(int)

    print("正在处理数据...")
    processed_count = 0

    # 遍历每一行数据
    for _, row in df.iterrows():
        author = row['原文作者']
        content = row['全文内容']

        # 跳过作者为空的行（包括空字符串、空格、单引号等）
        if pd.isna(author) or not author or author.strip() == '' or author.strip() == "''":
            continue

        # 提取@的用户名
        mentions = extract_at_mentions(content)

        # 为每个被@的用户创建记录（去重，同一条内容中多次@同一人只算一次）
        unique_mentions = list(set(mentions))  # 去重
        for mentioned_user in unique_mentions:
            # 创建(艾特者, 被艾特者)的键
            key = (author, mentioned_user)
            mention_count[key] += 1

        processed_count += 1
        if processed_count % 1000 == 0:
            print(f"已处理 {processed_count} 行数据...")

    print(f"数据处理完成，共处理 {processed_count} 行数据")
    print(f"找到 {len(mention_count)} 个唯一的@关系")

    # 创建结果DataFrame
    result_data = []
    for (author, mentioned_user), count in mention_count.items():
        # 去掉作者名称中的单引号
        clean_author = author.strip("'") if author else author

        # 再次检查清理后的作者名是否为空，如果为空则跳过
        if not clean_author or clean_author.strip() == '':
            continue

        result_data.append({
            '艾特者': clean_author,
            '被艾特者': mentioned_user,
            '权重': count
        })

    result_df = pd.DataFrame(result_data)

    # 按权重降序排列
    result_df = result_df.sort_values('权重', ascending=False)

    return result_df

# 执行主函数
if __name__ == "__main__":
    print("开始创建@关系网络表...")
    print("注意：同一条内容中多次@同一人只计算一次")

    # 创建@关系表
    mention_df = create_mention_network()

    # 保存结果
    output_file = '@关系网络表_最终版.csv'
    mention_df.to_csv(output_file, index=False, encoding='utf-8-sig')

    print(f"\n结果已保存到: {output_file}")
    print(f"共生成 {len(mention_df)} 条@关系记录")

    # 显示前10条结果
    print("\n前10条@关系记录：")
    print(mention_df.head(10).to_string(index=False))

    # 显示一些统计信息
    print(f"\n统计信息：")
    print(f"总共有 {mention_df['艾特者'].nunique()} 个不同的艾特者")
    print(f"总共有 {mention_df['被艾特者'].nunique()} 个不同的被艾特者")
    print(f"最高权重: {mention_df['权重'].max()}")
    print(f"平均权重: {mention_df['权重'].mean():.2f}")

    # 验证观察者网的结果
    print(f"\n验证'观察者网'@'广州天河公安'的结果：")
    guancha_record = mention_df[(mention_df['艾特者'] == '观察者网') & (mention_df['被艾特者'] == '广州天河公安')]
    if not guancha_record.empty:
        count = guancha_record['权重'].iloc[0]
        print(f"'观察者网' @'广州天河公安' 的权重: {count}")
    else:
        print("未找到'观察者网' @'广州天河公安'的记录")