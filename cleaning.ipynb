{"cells": [{"cell_type": "code", "execution_count": 1, "id": "437a64c1", "metadata": {}, "outputs": [], "source": ["import pandas as pd"]}, {"cell_type": "code", "execution_count": null, "id": "50366672", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>标题／微博内容</th>\n", "      <th>信息属性</th>\n", "      <th>原创/转发</th>\n", "      <th>原文/评论链接</th>\n", "      <th>来源网站</th>\n", "      <th>原文作者</th>\n", "      <th>日期</th>\n", "      <th>媒体类型</th>\n", "      <th>原微博内容</th>\n", "      <th>认证类型</th>\n", "      <th>...</th>\n", "      <th>用户画像用户画像@的内容</th>\n", "      <th>用户画像@别人的标签</th>\n", "      <th>用户画像用户认证信息</th>\n", "      <th>用户画像出生年份</th>\n", "      <th>用户画像公司名称</th>\n", "      <th>用户画像感情状况</th>\n", "      <th>用户画像用户兴趣标签</th>\n", "      <th>用户画像学校名称</th>\n", "      <th>用户画像关注的话题</th>\n", "      <th>Column1</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>购彩中心-购彩大厅平台</td>\n", "      <td>非敏感</td>\n", "      <td>原创</td>\n", "      <td>http://www.zghechuan.com/heshort/</td>\n", "      <td>合新网</td>\n", "      <td>''</td>\n", "      <td>2024-03-13 22:05:58</td>\n", "      <td>网站</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>...</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>NaN</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>聯系购彩中心-购彩大厅</td>\n", "      <td>敏感</td>\n", "      <td>原创</td>\n", "      <td>http://www.zghechuan.com/feltturkey/</td>\n", "      <td>合新网</td>\n", "      <td>''</td>\n", "      <td>2024-03-14 23:15:33</td>\n", "      <td>网站</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>...</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>NaN</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>中央民族乐团航天音乐会在海南文昌奏响</td>\n", "      <td>敏感</td>\n", "      <td>原创</td>\n", "      <td>http://www.zghechuan.com/Z_05606979.html</td>\n", "      <td>合新网</td>\n", "      <td>'购彩中心'</td>\n", "      <td>2024-03-17 22:00:01</td>\n", "      <td>网站</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>...</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>NaN</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>聯系购彩中心-购彩大厅</td>\n", "      <td>敏感</td>\n", "      <td>原创</td>\n", "      <td>http://www.zghechuan.com/957543/</td>\n", "      <td>合新网</td>\n", "      <td>''</td>\n", "      <td>2024-03-11 22:04:48</td>\n", "      <td>网站</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>...</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>NaN</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>聯系购彩中心-购彩大厅</td>\n", "      <td>敏感</td>\n", "      <td>原创</td>\n", "      <td>http://www.zghechuan.com/419754/</td>\n", "      <td>合新网</td>\n", "      <td>''</td>\n", "      <td>2024-02-26 23:48:17</td>\n", "      <td>网站</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>...</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>NaN</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 49 columns</p>\n", "</div>"], "text/plain": ["              标题／微博内容 信息属性 原创/转发                                   原文/评论链接  \\\n", "0         购彩中心-购彩大厅平台  非敏感    原创         http://www.zghechuan.com/heshort/   \n", "1         聯系购彩中心-购彩大厅   敏感    原创      http://www.zghechuan.com/feltturkey/   \n", "2  中央民族乐团航天音乐会在海南文昌奏响   敏感    原创  http://www.zghechuan.com/Z_05606979.html   \n", "3         聯系购彩中心-购彩大厅   敏感    原创          http://www.zghechuan.com/957543/   \n", "4         聯系购彩中心-购彩大厅   敏感    原创          http://www.zghechuan.com/419754/   \n", "\n", "  来源网站    原文作者                   日期 媒体类型 原微博内容 认证类型  ... 用户画像用户画像@的内容  \\\n", "0  合新网      ''  2024-03-13 22:05:58   网站             ...                \n", "1  合新网      ''  2024-03-14 23:15:33   网站             ...                \n", "2  合新网  '购彩中心'  2024-03-17 22:00:01   网站             ...                \n", "3  合新网      ''  2024-03-11 22:04:48   网站             ...                \n", "4  合新网      ''  2024-02-26 23:48:17   网站             ...                \n", "\n", "  用户画像@别人的标签 用户画像用户认证信息 用户画像出生年份  用户画像公司名称  用户画像感情状况  用户画像用户兴趣标签 用户画像学校名称  \\\n", "0                            NaN                                            \n", "1                            NaN                                            \n", "2                            NaN                                            \n", "3                            NaN                                            \n", "4                            NaN                                            \n", "\n", "   用户画像关注的话题 Column1  \n", "0                NaN  \n", "1                NaN  \n", "2                NaN  \n", "3                NaN  \n", "4                NaN  \n", "\n", "[5 rows x 49 columns]"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["df = pd.read_csv('w9yqtsjdboss122250408101438884 (4).csv'，encoding='gb18030')\n"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.4"}}, "nbformat": 4, "nbformat_minor": 5}