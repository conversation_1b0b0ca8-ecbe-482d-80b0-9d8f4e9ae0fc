import pandas as pd
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
import numpy as np

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# 读取用户行为数据
df = pd.read_csv('userinfo5.csv')

# 检查数据基本信息
print("数据形状:", df.shape)
print("前5行数据:")
print(df.head())
print("\n数据类型:")
print(df.dtypes)

# 处理日期列 - 从Excel序列号转换为日期
# Excel的日期序列号是从1900年1月1日开始计算的天数
def excel_date_to_datetime(excel_date):
    if pd.isna(excel_date):
        return pd.NaT
    # Excel日期序列号转换为日期
    # 注意：Excel错误地认为1900年是闰年，所以需要减去2天
    base_date = datetime(1899, 12, 30)  # 修正后的基准日期
    return base_date + timedelta(days=excel_date)

# 转换日期列
df['日期_转换'] = df['日期'].apply(excel_date_to_datetime)
df['date'] = df['日期_转换'].dt.date

print("\n转换后的日期示例:")
print(df[['日期', '日期_转换', 'date']].head())

# 检查有效的作者ID
valid_authors = df['作者ID'].value_counts()
print(f"\n作者ID统计（前10个）:")
print(valid_authors.head(10))

# 按日聚合用户行为数据
daily_behavior = df.groupby(['作者ID', 'date']).agg({
    '转发数': 'sum',
    '评论数': 'sum',
    '点赞数': 'sum',
    '阅读数/浏览热度': 'sum'
}).reset_index()

print(f"\n聚合后的数据形状: {daily_behavior.shape}")
print("聚合后的数据示例:")
print(daily_behavior.head())

# 选择一个有数据的作者ID进行分析
if len(valid_authors) > 0:
    # 寻找有实际活动的作者（转发数、评论数、点赞数、阅读数不全为0）
    active_authors = []
    for author in valid_authors.index[:10]:  # 检查前10个最活跃的作者
        author_data_temp = daily_behavior[daily_behavior['作者ID'] == author]
        total_activity = (author_data_temp['转发数'].sum() +
                         author_data_temp['评论数'].sum() +
                         author_data_temp['点赞数'].sum() +
                         author_data_temp['阅读数/浏览热度'].sum())
        if total_activity > 0:
            active_authors.append((author, total_activity))

    if active_authors:
        # 选择活动最多的作者
        active_authors.sort(key=lambda x: x[1], reverse=True)
        selected_author = active_authors[0][0]
        print(f"\n选择的作者ID: {selected_author} (总活动量: {active_authors[0][1]})")
    else:
        # 如果没有活跃作者，选择发布内容最多的作者
        selected_author = valid_authors.index[0]
        print(f"\n选择的作者ID: {selected_author} (发布内容最多，但活动量可能为0)")

    # 获取该作者的时间序列数据
    author_data = daily_behavior[daily_behavior['作者ID'] == selected_author].copy()
    author_data = author_data.sort_values('date')

    print(f"该作者的数据点数: {len(author_data)}")
    print("该作者的数据统计:")
    print(author_data[['转发数', '评论数', '点赞数', '阅读数/浏览热度']].describe())
    print("\n该作者的前10条数据:")
    print(author_data.head(10))

    if len(author_data) > 1:
        # 设置日期为索引，只保留数值列
        numeric_cols = ['转发数', '评论数', '点赞数', '阅读数/浏览热度']
        author_data_numeric = author_data[['date'] + numeric_cols].copy()
        author_data_numeric.set_index('date', inplace=True)

        # 平滑处理（移动平均）
        window_size = min(3, len(author_data_numeric))  # 确保窗口大小不超过数据长度
        author_data_smoothed = author_data_numeric.rolling(window=window_size, min_periods=1).mean()

        # 绘制时间序列图
        plt.figure(figsize=(12, 8))

        plt.subplot(2, 2, 1)
        plt.plot(author_data_smoothed.index, author_data_smoothed['评论数'], 'b-', label='评论数', marker='o')
        plt.title('评论数时间序列')
        plt.xlabel('日期')
        plt.ylabel('评论数')
        plt.xticks(rotation=45)
        plt.grid(True)

        plt.subplot(2, 2, 2)
        plt.plot(author_data_smoothed.index, author_data_smoothed['转发数'], 'r-', label='转发数', marker='s')
        plt.title('转发数时间序列')
        plt.xlabel('日期')
        plt.ylabel('转发数')
        plt.xticks(rotation=45)
        plt.grid(True)

        plt.subplot(2, 2, 3)
        plt.plot(author_data_smoothed.index, author_data_smoothed['点赞数'], 'g-', label='点赞数', marker='^')
        plt.title('点赞数时间序列')
        plt.xlabel('日期')
        plt.ylabel('点赞数')
        plt.xticks(rotation=45)
        plt.grid(True)

        plt.subplot(2, 2, 4)
        plt.plot(author_data_smoothed.index, author_data_smoothed['阅读数/浏览热度'], 'm-', label='阅读数', marker='d')
        plt.title('阅读数时间序列')
        plt.xlabel('日期')
        plt.ylabel('阅读数')
        plt.xticks(rotation=45)
        plt.grid(True)

        plt.tight_layout()
        plt.suptitle(f"作者 {selected_author} 的行为时间序列分析", y=1.02)
        plt.show()

    else:
        print("该作者的数据点太少，无法进行时间序列分析")
else:
    print("没有找到有效的作者数据")

