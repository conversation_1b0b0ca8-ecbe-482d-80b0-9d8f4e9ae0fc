import pandas as pd
import numpy as np
from sklearn.preprocessing import StandardScaler, MinMaxScaler
from sklearn.cluster import KMeans
import matplotlib.pyplot as plt

# 假设你已经有数据（用你自己的数据替换这里）
df = pd.read_excel("用户特征汇总.xlsx")
df

# 设置索引为“原文作者”
df.set_index('原文作者', inplace=True)

# 聚类使用的字段（只有这四个）
features = ['粉丝数', '转发数_平均', '评论数_平均', '点赞数_平均']

# 0. 对原始数据进行 log1p 转换（防止 log(0) 报错）
df_log = np.log1p(df[features])

# 1. 标准化
scaler = StandardScaler()
X_scaled = scaler.fit_transform(df_log)

# 2. 聚类（KMeans）
k = 4
kmeans = KMeans(n_clusters=k, random_state=42)
df['cluster'] = kmeans.fit_predict(X_scaled)

# 3. 聚类后，每个簇的原始均值
# === 聚类后，把 log1p 后的数据和聚类标签结合起来 ===
df_log['cluster'] = df['cluster']

# === 用 log1p 数据计算每个簇的均值 ===
cluster_summary_log = df_log.groupby('cluster')[features].mean()


# 4. 对簇均值做 MinMax 标准化（雷达图专用）
mms = MinMaxScaler()
cluster_summary_log_scaled = pd.DataFrame(
    mms.fit_transform(cluster_summary_log),
    columns=cluster_summary_log.columns,
    index=cluster_summary_log.index
)

# 5. 雷达图函数
import matplotlib.pyplot as plt
plt.rcParams['font.family'] = 'SimHei'  # 黑体
plt.rcParams['axes.unicode_minus'] = False  # 显示负号
def plot_radar(data, title="用户簇行为雷达图（标准化后）"):
    categories = list(data.columns)
    N = len(categories)
    angles = np.linspace(0, 2*np.pi, N, endpoint=False).tolist()
    angles += angles[:1]

    plt.figure(figsize=(8,6))
    for idx, row in data.iterrows():
        values = row.tolist()
        values += values[:1]
        plt.polar(angles, values, label=f'簇 {idx}', linewidth=2)
    
    plt.xticks(angles[:-1], categories, size=10)
    plt.yticks([0.2,0.4,0.6,0.8,1.0], color='grey', size=8)
    plt.ylim(0,1)
    plt.title(title, fontsize=14)
    plt.legend(loc='upper right', bbox_to_anchor=(1.3,1.1))
    plt.tight_layout()
    plt.show()

# 6. 绘图
plot_radar(cluster_summary_log_scaled)

print(cluster_summary_log)
print(df.groupby('cluster')[features].mean())

cluster_counts = df['cluster'].value_counts().sort_index()
print(cluster_counts)

#贴标签
# 1. 定义每个簇的标签（按需修改）
cluster_labels = {
    0: '潜水者',
    1: '围观者',
    2: 'X',
    3: '意见领袖'
}

# 2. 映射标签
df['用户类型'] = df['cluster'].map(cluster_labels)

# 3. 保存到 Excel 文件
df.to_excel("用户聚类结果_带标签.xlsx", index=True)

