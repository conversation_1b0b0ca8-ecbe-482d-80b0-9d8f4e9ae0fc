import pandas as pd
import numpy as np
from sklearn.preprocessing import StandardScaler, MinMaxScaler
from sklearn.cluster import KMeans
import matplotlib.pyplot as plt

# 假设你已经有数据（用你自己的数据替换这里）
df = pd.read_excel("用户特征汇总.xlsx")
df

# 设置索引为“原文作者”
df.set_index('原文作者', inplace=True)

# 聚类使用的字段（只有这四个）
features = ['粉丝数', '转发数_平均', '评论数_平均', '点赞数_平均']

df['互动热度'] = df['转发数_平均'] + df['评论数_平均'] + df['点赞数_平均']
# 或加权方式：df['互动热度'] = 0.4*转发 + 0.3*评论 + 0.3*点赞

import numpy as np

df['log_粉丝数'] = np.log1p(df['粉丝数'])
df['log_互动热度'] = np.log1p(df['互动热度'])

from sklearn.cluster import KMeans

X = df[['log_粉丝数', 'log_互动热度']]
model = KMeans(n_clusters=4, random_state=42)
df['cluster'] = model.fit_predict(X)

centers = pd.DataFrame(model.cluster_centers_, columns=['log_粉丝数', 'log_互动热度'])
print(centers)

import matplotlib.pyplot as plt
plt.rcParams['font.family'] = 'SimHei'  # 黑体
plt.rcParams['axes.unicode_minus'] = False  # 显示负号
plt.figure(figsize=(8, 6))
plt.scatter(df['log_粉丝数'], df['log_互动热度'], c=df['cluster'], cmap='viridis', s=20)
plt.xlabel("log(粉丝数)")
plt.ylabel("log(互动热度)")
plt.title("用户聚类分布（粉丝数 × 互动热度）")
plt.colorbar(label='cluster')
plt.show()

cluster_counts = df['cluster'].value_counts().sort_index()
print(cluster_counts)

#贴标签
# 1. 定义每个簇的标签（按需修改）
cluster_labels = {
    0: '潜水者',
    1: '围观者',
    2: 'X',
    3: '意见领袖'
}

# 2. 映射标签
df['用户类型'] = df['cluster'].map(cluster_labels)

# 3. 保存到 Excel 文件
df.to_excel("用户聚类结果_带标签.xlsx", index=True)