import pandas as pd
import numpy as np

def load_data():
    """加载三个Excel文件"""
    print("正在加载数据文件...")

    # 加载艾特行为数据
    aite_df = pd.read_excel('艾特行为.xlsx')
    print(f"艾特行为数据: {len(aite_df)} 条记录")
    print(f"艾特行为数据列: {list(aite_df.columns)}")

    # 加载用户特征数据
    user_df = pd.read_excel('用户特征.xlsx')
    print(f"用户特征数据: {len(user_df)} 条记录")
    print(f"用户特征数据列: {list(user_df.columns)}")

    # 加载转发行为数据
    repost_df = pd.read_excel('转发行为.xlsx')
    print(f"转发行为数据: {len(repost_df)} 条记录")
    print(f"转发行为数据列: {list(repost_df.columns)}")

    return aite_df, user_df, repost_df

def process_network_data(df, prefix):
    """处理网络数据，重命名列"""
    print(f"正在处理{prefix}网络数据...")

    # 创建列名映射
    column_mapping = {
        '用户名称': '用户名称',
        'In-Degree': f'{prefix}的入度',
        'Out-Degree': f'{prefix}的出度',
        'Betweenness Centrality': f'{prefix}的中介中心性',
        'Closeness Centrality': f'{prefix}的接近中心性',
        'Eigenvector Centrality': f'{prefix}的特征向量中心性',
        'PageRank': f'{prefix}的pagerank'
    }

    # 重命名列
    processed_df = df.rename(columns=column_mapping)

    # 清理用户名称，确保是字符串类型并过滤无效用户名
    processed_df['用户名称'] = processed_df['用户名称'].astype(str)
    invalid_names = ['nan', '', ' ']
    processed_df = processed_df[~processed_df['用户名称'].isin(invalid_names)]
    processed_df = processed_df[processed_df['用户名称'].str.strip() != '']

    print(f"{prefix}网络数据清理后剩余 {len(processed_df)} 条有效记录")
    print(f"处理后的列名: {list(processed_df.columns)}")
    return processed_df

def merge_all_data(aite_df, user_df, repost_df):
    """合并所有数据"""
    print("开始合并数据...")

    # 处理转发网络指标
    print("\n=== 处理转发网络指标 ===")
    repost_metrics = process_network_data(repost_df, '转发')

    # 处理艾特网络指标
    print("\n=== 处理艾特网络指标 ===")
    aite_metrics = process_network_data(aite_df, '艾特')

    # 获取所有用户名称
    all_users = set()
    all_users.update(repost_metrics['用户名称'].tolist())
    all_users.update(aite_metrics['用户名称'].tolist())

    # 用户特征数据的用户名列可能是'原文作者'
    user_name_col = '原文作者' if '原文作者' in user_df.columns else '用户名称'
    if user_name_col in user_df.columns:
        all_users.update(user_df[user_name_col].tolist())

    print(f"总共发现 {len(all_users)} 个唯一用户")

    # 使用pandas merge进行合并
    # 从转发数据开始
    final_df = repost_metrics.copy()

    # 合并艾特数据
    final_df = pd.merge(final_df, aite_metrics, on='用户名称', how='outer')

    # 处理用户特征数据
    if user_name_col in user_df.columns:
        # 重命名用户名列以便合并
        user_features = user_df.copy()
        if user_name_col != '用户名称':
            user_features = user_features.rename(columns={user_name_col: '用户名称'})

        # 清理用户名称列，确保是字符串类型，并过滤无效用户名
        user_features['用户名称'] = user_features['用户名称'].astype(str)

        # 过滤掉无效的用户名称（NaN转换的'nan'、空字符串、单空格等）
        invalid_names = ['nan', '', ' ']
        user_features = user_features[~user_features['用户名称'].isin(invalid_names)]
        user_features = user_features[user_features['用户名称'].str.strip() != '']

        print(f"用户特征数据清理后剩余 {len(user_features)} 条有效记录")

        # 选择需要的列
        feature_cols = ['用户名称']
        available_feature_cols = ['粉丝数', '微博数', '转发数', '评论数', '点赞数', '阅读数/浏览热度']
        for col in available_feature_cols:
            if col in user_features.columns:
                feature_cols.append(col)

        user_features = user_features[feature_cols]

        # 确保数值列是数值类型
        numeric_cols = [col for col in feature_cols if col != '用户名称']
        for col in numeric_cols:
            user_features[col] = pd.to_numeric(user_features[col], errors='coerce')

        # 重命名列以匹配需求
        column_rename = {
            '转发数': '被转发数',
            '评论数': '被评论数',
            '点赞数': '被点赞数',
            '阅读数/浏览热度': '阅读数'
        }
        user_features = user_features.rename(columns=column_rename)

        # 网络数据的用户名称已经在process_network_data中清理过了

        # 合并用户特征
        final_df = pd.merge(final_df, user_features, on='用户名称', how='outer')

    # 填充缺失值
    numeric_cols = final_df.select_dtypes(include=[np.number]).columns
    final_df[numeric_cols] = final_df[numeric_cols].fillna(0)

    return final_df

def main():
    """主函数"""
    try:
        # 加载数据
        aite_df, user_df, repost_df = load_data()
        
        # 显示数据结构
        print("\n=== 数据结构检查 ===")
        print("艾特行为数据列:", list(aite_df.columns))
        print("用户特征数据列:", list(user_df.columns))
        print("转发行为数据列:", list(repost_df.columns))
        
        # 合并数据
        final_df = merge_all_data(aite_df, user_df, repost_df)
        
        # 保存结果
        output_file = '用户网络分析综合表.xlsx'
        final_df.to_excel(output_file, index=False)
        
        print(f"\n=== 处理完成 ===")
        print(f"结果已保存到: {output_file}")
        print(f"总共包含 {len(final_df)} 个用户")
        print(f"总共包含 {len(final_df.columns)} 个指标")
        
        # 显示前5行结果
        print("\n前5行数据预览:")
        print(final_df.head().to_string())
        
        # 显示统计信息
        print(f"\n=== 统计信息 ===")
        numeric_cols = final_df.select_dtypes(include=[np.number]).columns
        print(f"数值型指标统计:")
        print(final_df[numeric_cols].describe())
        
    except Exception as e:
        print(f"处理过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
