import pandas as pd
import numpy as np

def create_user_features_summary():
    """
    创建用户特征汇总表
    计算每个用户的出现次数和各项指标的平均值
    """
    print("正在读取用户特征数据...")
    
    # 读取用户特征数据
    df = pd.read_excel('用户特征.xlsx')
    print(f"原始数据: {len(df)} 条记录")
    print(f"数据列: {list(df.columns)}")
    
    # 检查数据结构
    print(f"\n数据概览:")
    print(df.head())
    
    # 清理数据 - 过滤掉无效的用户名
    print(f"\n清理前数据统计:")
    print(f"总记录数: {len(df)}")
    print(f"原文作者为空(NaN)的记录: {df['原文作者'].isna().sum()}")
    print(f"原文作者为空字符串的记录: {(df['原文作者'] == '').sum()}")
    print(f"原文作者为单空格的记录: {(df['原文作者'] == ' ').sum()}")
    
    # 过滤无效用户名
    # 先转换为字符串类型，然后过滤
    df['原文作者'] = df['原文作者'].astype(str)
    invalid_names = ['nan', '', ' ']
    df_clean = df[~df['原文作者'].isin(invalid_names)]
    df_clean = df_clean[df_clean['原文作者'].str.strip() != '']
    
    print(f"\n清理后数据统计:")
    print(f"有效记录数: {len(df_clean)}")
    print(f"唯一用户数: {df_clean['原文作者'].nunique()}")
    
    # 确保数值列是数值类型
    numeric_columns = ['粉丝数', '微博数', '转发数', '评论数', '点赞数', '阅读数/浏览热度']
    
    print(f"\n转换数值列...")
    for col in numeric_columns:
        if col in df_clean.columns:
            df_clean[col] = pd.to_numeric(df_clean[col], errors='coerce')
            print(f"{col}: 转换完成，NaN值数量: {df_clean[col].isna().sum()}")
    
    # 填充NaN值为0
    df_clean[numeric_columns] = df_clean[numeric_columns].fillna(0)
    
    print(f"\n开始计算用户汇总统计...")
    
    # 按用户分组计算统计信息
    user_summary = df_clean.groupby('原文作者').agg({
        '粉丝数': ['first', 'count'],  # 粉丝数取第一个值（假设同一用户粉丝数相同），count计算出现次数
        '微博数': 'first',  # 微博数取第一个值
        '转发数': 'mean',   # 转发数计算平均值
        '评论数': 'mean',   # 评论数计算平均值
        '点赞数': 'mean',   # 点赞数计算平均值
        '阅读数/浏览热度': 'mean'  # 阅读数计算平均值
    }).round(2)  # 保留2位小数
    
    # 重新整理列名
    user_summary.columns = [
        '粉丝数', '出现次数', '微博数', 
        '转发数_平均', '评论数_平均', '点赞数_平均', '阅读数_平均'
    ]
    
    # 重置索引，让原文作者成为一列
    user_summary = user_summary.reset_index()
    
    # 重新排列列的顺序
    final_columns = [
        '原文作者', '出现次数', '粉丝数', '微博数', 
        '转发数_平均', '评论数_平均', '点赞数_平均', '阅读数_平均'
    ]
    user_summary = user_summary[final_columns]
    
    # 按出现次数降序排列
    user_summary = user_summary.sort_values('出现次数', ascending=False)
    
    print(f"\n汇总统计完成:")
    print(f"唯一用户数: {len(user_summary)}")
    print(f"最高出现次数: {user_summary['出现次数'].max()}")
    print(f"平均出现次数: {user_summary['出现次数'].mean():.2f}")
    
    return user_summary

def main():
    """主函数"""
    try:
        # 创建用户特征汇总表
        summary_df = create_user_features_summary()
        
        # 保存结果
        output_file = '用户特征汇总.xlsx'
        summary_df.to_excel(output_file, index=False)
        
        print(f"\n=== 处理完成 ===")
        print(f"结果已保存到: {output_file}")
        print(f"汇总表包含 {len(summary_df)} 个用户")
        
        # 显示前10行结果
        print(f"\n前10个用户的汇总信息:")
        print(summary_df.head(10).to_string(index=False))
        
        # 显示统计信息
        print(f"\n=== 统计信息 ===")
        print(f"出现次数统计:")
        print(f"  最小值: {summary_df['出现次数'].min()}")
        print(f"  最大值: {summary_df['出现次数'].max()}")
        print(f"  平均值: {summary_df['出现次数'].mean():.2f}")
        print(f"  中位数: {summary_df['出现次数'].median()}")
        
        print(f"\n各指标平均值的统计:")
        numeric_cols = ['转发数_平均', '评论数_平均', '点赞数_平均', '阅读数_平均']
        for col in numeric_cols:
            print(f"  {col}: 平均 {summary_df[col].mean():.2f}, 最大 {summary_df[col].max():.2f}")
        
        # 显示出现次数最多的前5个用户
        print(f"\n出现次数最多的前5个用户:")
        top_users = summary_df.head(5)[['原文作者', '出现次数', '粉丝数', '微博数']]
        print(top_users.to_string(index=False))
        
    except Exception as e:
        print(f"处理过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
