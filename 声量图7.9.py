import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
from datetime import datetime

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False



def process_date_column(df):
    """处理日期列，转换为标准日期格式"""
    print("正在处理日期列...")

    # 尝试直接解析日期
    try:
        df['日期_处理'] = pd.to_datetime(df['日期'], errors='coerce')
        print("日期解析成功")
    except:
        print("日期解析失败，尝试其他方法...")
        df['日期_处理'] = pd.to_datetime(df['日期'], errors='coerce')

    # 检查解析结果
    valid_dates = df['日期_处理'].notna().sum()
    total_dates = len(df)
    print(f"成功解析日期: {valid_dates}/{total_dates} ({valid_dates/total_dates*100:.1f}%)")

    # 只保留有效日期的数据
    df_valid = df[df['日期_处理'].notna()].copy()

    # 提取日期部分（去掉时间）
    df_valid['日期_日'] = df_valid['日期_处理'].dt.date

    return df_valid

def calculate_slope(x1, y1, x2, y2):
    """计算两点之间的斜率，x为日期"""
    # 将日期转换为数值（天数差）
    if hasattr(x1, 'toordinal'):
        x1_num = x1.toordinal()
        x2_num = x2.toordinal()
    else:
        x1_num = pd.to_datetime(x1).toordinal()
        x2_num = pd.to_datetime(x2).toordinal()

    if x2_num == x1_num:
        return 0
    return (y2 - y1) / (x2_num - x1_num)

def find_auxiliary_points(total_data):
    """找到总计线峰值左右的辅助线点"""
    print("正在寻找辅助线点...")

    # 找到峰值点
    peak_idx = total_data['数量'].idxmax()
    peak_value = total_data.loc[peak_idx, '数量']
    peak_date = total_data.loc[peak_idx, '日期_日']

    print(f"峰值点: 日期={peak_date}, 数量={peak_value}, 索引={peak_idx}")

    # 计算图表刻度间距
    # Y轴刻度间距：数据范围
    y_min = total_data['数量'].min()
    y_max = total_data['数量'].max()
    y_range = y_max - y_min

    # X轴刻度间距：时间范围（天数）
    x_min_date = total_data['日期_日'].min()
    x_max_date = total_data['日期_日'].max()
    x_range_days = (x_max_date - x_min_date).days

    # 计算斜率阈值：y刻度间距 / x刻度间距
    # 这里我们使用合理的刻度间距估算
    # Y轴：假设有10个主要刻度，每个刻度间距为 y_range/10
    # X轴：每天为一个单位
    y_tick_spacing = y_range / 10  # Y轴刻度间距
    x_tick_spacing = 1  # X轴刻度间距（天）

    slope_threshold = y_tick_spacing / x_tick_spacing

    print(f"Y轴数据范围: {y_min} 到 {y_max}, 范围={y_range}")
    print(f"X轴时间范围: {x_min_date} 到 {x_max_date}, 天数={x_range_days}")
    print(f"Y轴刻度间距估算: {y_tick_spacing:.2f}")
    print(f"X轴刻度间距: {x_tick_spacing} 天")
    print(f"斜率阈值 |k| > {slope_threshold:.2f}")

    # 计算所有相邻点的斜率
    slopes = []
    print("\n=== 斜率计算详情 ===")
    for i in range(len(total_data) - 1):
        x1 = total_data.iloc[i]['日期_日']
        y1 = total_data.iloc[i]['数量']
        x2 = total_data.iloc[i + 1]['日期_日']
        y2 = total_data.iloc[i + 1]['数量']
        slope = calculate_slope(x1, y1, x2, y2)
        slopes.append(slope)
        print(f"点{i}到点{i+1}: ({x1}, {y1}) -> ({x2}, {y2}), 斜率 = {slope:.4f}")

    print(f"\n所有斜率: {[round(s, 4) for s in slopes]}")

    # 找到峰值左侧最近的斜率符号变化点（A左点）
    left_a_idx = None
    print(f"\n=== 寻找左侧A点 ===")
    print(f"从峰值索引{peak_idx}向左搜索斜率符号变化点...")
    for i in range(peak_idx - 1, 0, -1):  # 从峰值向左搜索
        if i - 1 < len(slopes) and i < len(slopes):
            current_slope = slopes[i] if i >= 0 else 0
            prev_slope = slopes[i-1] if i - 1 >= 0 else 0

            print(f"检查索引{i}: 后一斜率={current_slope:.4f}, 前一斜率={prev_slope:.4f}")

            # 检查斜率符号是否相反
            if (current_slope > 0 and prev_slope < 0) or (current_slope < 0 and prev_slope > 0):
                left_a_idx = i
                print(f"找到左侧A点! 索引={i}, 斜率符号变化: {prev_slope:.4f} -> {current_slope:.4f}")
                break

    # 如果没有找到斜率符号变化点，使用左侧最远点
    if left_a_idx is None:
        left_a_idx = 0  # x优化范围内离峰值最远的点（最左侧点）
        print(f"未找到左侧斜率符号变化点，使用最远点作为A左点: 索引={left_a_idx}")

    # 找到峰值右侧最近的斜率符号变化点（A右点）
    right_a_idx = None
    print(f"\n=== 寻找右侧A点 ===")
    print(f"从峰值索引{peak_idx}向右搜索斜率符号变化点...")
    for i in range(peak_idx + 1, len(total_data) - 1):  # 从峰值向右搜索
        if i < len(slopes) and i + 1 < len(slopes):
            current_slope = slopes[i]
            next_slope = slopes[i + 1] if i + 1 < len(slopes) else 0

            print(f"检查索引{i}: 当前斜率={current_slope:.4f}, 下一斜率={next_slope:.4f}")

            # 检查斜率符号是否相反
            if (current_slope > 0 and next_slope < 0) or (current_slope < 0 and next_slope > 0):
                right_a_idx = i + 1
                print(f"找到右侧A点! 索引={i+1}, 斜率符号变化: {current_slope:.4f} -> {next_slope:.4f}")
                break

    # 如果没有找到斜率符号变化点，使用右侧最远点
    if right_a_idx is None:
        right_a_idx = len(total_data) - 1  # x优化范围内离峰值最远的点（最右侧点）
        print(f"未找到右侧斜率符号变化点，使用最远点作为A右点: 索引={right_a_idx}")

    print(f"A左点索引: {left_a_idx}, A右点索引: {right_a_idx}")

    # 从A点向峰值方向找到|k|>25的点
    left_aux_idx = None
    right_aux_idx = None

    # 左侧辅助点
    print(f"\n=== 寻找左侧辅助点 ===")
    print(f"从A左点索引{left_a_idx}向峰值索引{peak_idx}搜索|k|>{slope_threshold:.2f}的点...")
    for i in range(left_a_idx, peak_idx):
        if i + 1 < len(total_data):
            x1 = total_data.iloc[i]['日期_日']
            y1 = total_data.iloc[i]['数量']
            x2 = total_data.iloc[i + 1]['日期_日']
            y2 = total_data.iloc[i + 1]['数量']
            slope = calculate_slope(x1, y1, x2, y2)

            print(f"检查索引{i}到{i+1}: ({x1}, {y1}) -> ({x2}, {y2}), 斜率={slope:.4f}, |k|={abs(slope):.4f}")

            if abs(slope) > slope_threshold:
                left_aux_idx = i
                print(f"找到左侧辅助点! 索引={i}, |k|={abs(slope):.4f} > {slope_threshold:.2f}")
                break

    if left_aux_idx is None:
        print(f"未找到满足|k|>{slope_threshold:.2f}条件的左侧辅助点")
        # 如果无法找到左侧辅助点，使用峰值点作为左侧辅助点
        if left_a_idx == peak_idx:
            left_aux_idx = peak_idx
            print(f"使用峰值点作为左侧辅助点: 索引={peak_idx}")

    # 右侧辅助点
    print(f"\n=== 寻找右侧辅助点 ===")
    print(f"从A右点索引{right_a_idx}向峰值索引{peak_idx}搜索|k|>{slope_threshold:.2f}的点...")
    for i in range(right_a_idx, peak_idx, -1):  # 从A右点向峰值方向搜索
        if i - 1 >= 0:
            x1 = total_data.iloc[i - 1]['日期_日']
            y1 = total_data.iloc[i - 1]['数量']
            x2 = total_data.iloc[i]['日期_日']
            y2 = total_data.iloc[i]['数量']
            slope = calculate_slope(x1, y1, x2, y2)

            print(f"检查索引{i-1}到{i}: ({x1}, {y1}) -> ({x2}, {y2}), 斜率={slope:.4f}, |k|={abs(slope):.4f}")

            if abs(slope) > slope_threshold:
                right_aux_idx = i
                print(f"找到右侧辅助点! 索引={i}, |k|={abs(slope):.4f} > {slope_threshold:.2f}")
                break

    if right_aux_idx is None:
        print(f"未找到满足|k|>{slope_threshold:.2f}条件的右侧辅助点")
        # 如果无法找到右侧辅助点，使用x优化范围内最右侧的点
        right_aux_idx = len(total_data) - 1
        print(f"使用x优化范围内最右侧的点作为右侧辅助点: 索引={right_aux_idx}")

    result = {
        'peak_idx': peak_idx,
        'peak_date': peak_date,
        'peak_value': peak_value,
        'left_aux_idx': left_aux_idx,
        'right_aux_idx': right_aux_idx,
        'left_a_idx': left_a_idx,
        'right_a_idx': right_a_idx
    }

    # 添加辅助点的详细信息
    if left_aux_idx is not None:
        result['left_aux_date'] = total_data.iloc[left_aux_idx]['日期_日']
        result['left_aux_value'] = total_data.iloc[left_aux_idx]['数量']
        print(f"左侧辅助点: 日期={result['left_aux_date']}, 数量={result['left_aux_value']}, 索引={left_aux_idx}")

    if right_aux_idx is not None:
        result['right_aux_date'] = total_data.iloc[right_aux_idx]['日期_日']
        result['right_aux_value'] = total_data.iloc[right_aux_idx]['数量']
        print(f"右侧辅助点: 日期={result['right_aux_date']}, 数量={result['right_aux_value']}, 索引={right_aux_idx}")

    return result

def create_stacked_bar_chart(df, auxiliary_points):
    """创建按时期划分的堆叠柱状图"""
    print("\n开始创建堆叠柱状图...")

    # 获取所有非空的用户划分类别
    user_categories = df[df['用户划分'].notna() & (df['用户划分'].str.strip() != '')]['用户划分'].unique()
    user_categories = [cat for cat in user_categories if cat.strip() != '']

    if len(user_categories) == 0:
        print("没有有效的用户划分数据，无法绘制堆叠柱状图")
        return

    # 只使用有用户划分的数据
    valid_data = df[df['用户划分'].notna() & (df['用户划分'].str.strip() != '')].copy()

    # 获取辅助线日期
    if auxiliary_points['left_aux_idx'] is not None and auxiliary_points['right_aux_idx'] is not None:
        left_aux_date = auxiliary_points['left_aux_date']
        right_aux_date = auxiliary_points['right_aux_date']

        print(f"左侧辅助线日期: {left_aux_date}")
        print(f"右侧辅助线日期: {right_aux_date}")

        # 按时期划分数据
        # 潜伏期：左辅助线之前
        latent_data = valid_data[valid_data['日期_日'] < left_aux_date]
        # 爆发期：左辅助线到右辅助线之间
        outbreak_data = valid_data[(valid_data['日期_日'] >= left_aux_date) & (valid_data['日期_日'] <= right_aux_date)]
        # 衰退期：右辅助线之后
        decline_data = valid_data[valid_data['日期_日'] > right_aux_date]

        print(f"潜伏期数据量: {len(latent_data)}")
        print(f"爆发期数据量: {len(outbreak_data)}")
        print(f"衰退期数据量: {len(decline_data)}")

        # 统计各时期各用户类别的数量
        periods = ['潜伏期', '爆发期', '衰退期']
        period_data = [latent_data, outbreak_data, decline_data]

        # 创建数据矩阵
        data_matrix = []
        for period_df in period_data:
            period_counts = []
            total_count = len(period_df)
            for category in user_categories:
                count = len(period_df[period_df['用户划分'] == category])
                percentage = (count / total_count * 100) if total_count > 0 else 0
                period_counts.append(percentage)
            data_matrix.append(period_counts)

        # 转置矩阵，使每行代表一个用户类别
        data_matrix = list(map(list, zip(*data_matrix)))

        # 创建堆叠柱状图
        fig, ax = plt.subplots(figsize=(12, 8))

        # 设置颜色
        colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd', '#8c564b', '#e377c2']

        # 绘制堆叠柱状图
        bottom = [0] * len(periods)
        bars = []

        for i, category in enumerate(user_categories):
            color = colors[i % len(colors)]
            bar = ax.bar(periods, data_matrix[i], bottom=bottom,
                        label=category, color=color, alpha=0.8)
            bars.append(bar)

            # 更新底部位置
            bottom = [bottom[j] + data_matrix[i][j] for j in range(len(periods))]

            # 在柱状图上添加百分比标签
            for j, (period, value) in enumerate(zip(periods, data_matrix[i])):
                if value > 2:  # 只显示大于2%的标签，避免太小的标签重叠
                    height = bottom[j] - value/2  # 标签位置在该段的中间
                    ax.text(j, height, f'{value:.1f}%',
                           ha='center', va='center', fontsize=10,
                           color='white', weight='bold')

        # 设置图表属性
        ax.set_title('各时期用户类型分布（堆叠柱状图）', fontsize=16, fontweight='bold', pad=20)
        ax.set_xlabel('时期', fontsize=12)
        ax.set_ylabel('用户比例 (%)', fontsize=12)
        ax.legend(loc='upper right', fontsize=10)
        ax.set_ylim(0, 100)

        # 添加网格
        ax.grid(True, alpha=0.3, axis='y')

        # 在每个柱子上方显示总数
        for j, period in enumerate(periods):
            total = len(period_data[j])
            ax.text(j, 102, f'总计: {total}条',
                   ha='center', va='bottom', fontsize=11, weight='bold')

        # 调整布局
        plt.tight_layout()

        # 保存图片
        plt.savefig('用户类型时期分布堆叠图-335南江城管与卖菜老人推搡事件.png', dpi=300, bbox_inches='tight')
        print("堆叠柱状图已保存为 '用户类型时期分布堆叠图.png'")

        # 显示详细统计
        print(f"\n=== 各时期用户类型分布统计 ===")
        for i, period in enumerate(periods):
            print(f"\n{period}:")
            total = len(period_data[i])
            print(f"  总计: {total} 条")
            for j, category in enumerate(user_categories):
                count = len(period_data[i][period_data[i]['用户划分'] == category])
                percentage = (count / total * 100) if total > 0 else 0
                print(f"  {category}: {count} 条 ({percentage:.1f}%)")

        plt.close()
    else:
        print("未找到有效的辅助线点，无法创建堆叠柱状图")

def create_user_category_timeline(df):
    """创建用户划分时间线图表"""
    print("开始创建用户划分时间线图表...")

    # 获取所有非空的用户划分类别（过滤掉空字符串和空白字符串）
    user_categories = df[df['用户划分'].notna() & (df['用户划分'].str.strip() != '')]['用户划分'].unique()
    # 进一步过滤掉只包含空白字符的类别
    user_categories = [cat for cat in user_categories if cat.strip() != '']
    print(f"用户划分类别: {user_categories}")
    print(f"用户划分类别数量: {len(user_categories)}")

    if len(user_categories) == 0:
        print("没有有效的用户划分数据，无法绘制图表")
        return

    # 只使用有用户划分的数据（过滤掉空字符串和空白字符串）
    valid_data = df[df['用户划分'].notna() & (df['用户划分'].str.strip() != '')].copy()
    print(f"有效数据量: {len(valid_data)}")

    # 按日期和用户划分分组统计
    daily_user_counts = valid_data.groupby(['日期_日', '用户划分']).size().reset_index(name='数量')

    # 创建完整的日期范围
    full_date_range = pd.date_range(
        start=valid_data['日期_日'].min(),
        end=valid_data['日期_日'].max(),
        freq='D'
    ).date

    # 为每个用户类别创建完整的时间序列
    all_data = []
    for category in user_categories:
        category_data = daily_user_counts[daily_user_counts['用户划分'] == category]

        # 创建完整日期序列，缺失日期填充为0
        full_series = pd.DataFrame({'日期_日': full_date_range})
        full_series = full_series.merge(category_data[['日期_日', '数量']], on='日期_日', how='left')
        full_series['数量'] = full_series['数量'].fillna(0)
        full_series['用户划分'] = category

        all_data.append(full_series)

    # 合并所有数据
    plot_data = pd.concat(all_data, ignore_index=True)

    # 优化日期范围：基于峰值的5%阈值来确定有效范围
    print("正在优化日期范围...")

    # 按日期计算每天所有用户类别的总数
    daily_totals = plot_data.groupby('日期_日')['数量'].sum().reset_index()
    daily_totals = daily_totals.sort_values('日期_日').reset_index(drop=True)

    if len(daily_totals) == 0:
        print("没有找到数据")
        return

    # 找到峰值
    peak_value = daily_totals['数量'].max()
    threshold = peak_value * 0.05  # 峰值的5%

    print(f"峰值: {peak_value}")
    print(f"阈值(峰值的5%): {threshold}")

    # 从最小日期开始向右查找
    start_idx = 0
    for i in range(len(daily_totals) - 2):  # 确保有下一个和下下一个点
        current_val = daily_totals.iloc[i]['数量']
        next_val = daily_totals.iloc[i + 1]['数量']
        next_next_val = daily_totals.iloc[i + 2]['数量']

        print(f"从左检查索引{i}: 当前={current_val:.1f}, 下一个={next_val:.1f}, 下下一个={next_next_val:.1f}")

        # 如果当前点、下一个点、下下一个点都小于阈值，继续向右
        if current_val < threshold and next_val < threshold and next_next_val < threshold:
            start_idx = i + 1  # 移动到下一个点
            print(f"  三个点都小于阈值，移动到索引{start_idx}")
        else:
            # 情况改变，停止
            print(f"  情况改变，确定左边界为索引{start_idx}")
            break

    # 从最大日期开始向左查找
    end_idx = len(daily_totals) - 1
    for i in range(len(daily_totals) - 1, 1, -1):  # 从右向左，确保有前一个和前前一个点
        current_val = daily_totals.iloc[i]['数量']
        prev_val = daily_totals.iloc[i - 1]['数量']
        prev_prev_val = daily_totals.iloc[i - 2]['数量']

        print(f"从右检查索引{i}: 当前={current_val:.1f}, 前一个={prev_val:.1f}, 前前一个={prev_prev_val:.1f}")

        # 如果当前点、前一个点、前前一个点都小于阈值，继续向左
        if current_val < threshold and prev_val < threshold and prev_prev_val < threshold:
            end_idx = i - 1  # 移动到前一个点
            print(f"  三个点都小于阈值，移动到索引{end_idx}")
        else:
            # 情况改变，停止
            print(f"  情况改变，确定右边界为索引{end_idx}")
            break

    # 确保索引有效
    start_idx = max(0, start_idx)
    end_idx = min(len(daily_totals) - 1, end_idx)

    # 获取优化后的日期范围
    start_date = daily_totals.iloc[start_idx]['日期_日']
    end_date = daily_totals.iloc[end_idx]['日期_日']

    print(f"确定的日期范围: 索引{start_idx}到{end_idx}")
    print(f"对应日期: {start_date} 到 {end_date}")

    # 过滤数据到优化后的日期范围
    plot_data = plot_data[(plot_data['日期_日'] >= start_date) & (plot_data['日期_日'] <= end_date)]

    print(f"原始日期范围: {len(full_date_range)} 天")
    print(f"优化后日期范围: {end_idx - start_idx + 1} 天")
    print(f"删除了 {len(full_date_range) - (end_idx - start_idx + 1)} 天的无效数据")
    print(f"优化后时间范围: {start_date} 至 {end_date}")

    # 计算总计线数据
    total_data = plot_data.groupby('日期_日')['数量'].sum().reset_index()
    total_data = total_data.sort_values('日期_日')

    # 找到峰值和辅助线点
    auxiliary_points = find_auxiliary_points(total_data)

    # 计算峰值的5%作为标签显示阈值
    peak_value = total_data['数量'].max()
    label_threshold = peak_value * 0.05
    print(f"标签显示阈值(峰值的5%): {label_threshold}")

    # 绘制图表
    plt.figure(figsize=(16, 10))  # 增大图表尺寸以容纳标签

    # 为每个用户类别定义颜色
    colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd', '#8c564b', '#e377c2', '#7f7f7f']

    # 先绘制总计线（使用粗线和特殊样式）
    plt.plot(total_data['日期_日'], total_data['数量'],
            marker='s', linewidth=4, markersize=8,
            color='black', label='总计', linestyle='-', alpha=0.8)

    # 为总计线添加标签（只显示大于阈值的点）
    for idx, row in total_data.iterrows():
        x_val = row['日期_日']
        y_val = row['数量']

        # 只为大于等于阈值的点添加标签
        if y_val >= label_threshold:
            date_str = x_val.strftime('%m-%d')
            label_text = f'({date_str}, {int(y_val)})'

            # 总计线标签放在最上方，使用更大的字体
            plt.annotate(label_text,
                       (x_val, y_val),
                       xytext=(0, 20),  # 向上偏移更多
                       textcoords='offset points',
                       fontsize=14,  # 更大的字体
                       color='black',
                       alpha=1.0,
                       bbox=dict(boxstyle='round,pad=0.4',
                               facecolor='yellow',  # 黄色背景突出显示
                               edgecolor='black',
                               alpha=0.9),
                       ha='center',
                       weight='bold')

    # 然后绘制各个用户类别的线条
    for i, category in enumerate(user_categories):
        if category == '':
            continue

        category_data = plot_data[plot_data['用户划分'] == category]
        color = colors[i % len(colors)]

        # 绘制线条和点
        plt.plot(category_data['日期_日'], category_data['数量'],
                marker='o', linewidth=2, markersize=5,
                color=color, label=category)

        # 为每个点添加坐标标签（只显示大于阈值的点）
        for idx, row in category_data.iterrows():
            x_val = row['日期_日']
            y_val = row['数量']

            # 只为大于等于阈值的点添加标签
            if y_val >= label_threshold:
                # 格式化日期显示（只显示月-日）
                date_str = x_val.strftime('%m-%d')
                label_text = f'({date_str}, {int(y_val)})'

                # 根据y值调整标签位置，避免重叠
                if y_val < label_threshold * 2:  # 相对较小的值
                    # 较小值点的标签
                    xytext = (5, 12)
                    fontsize = 11
                    alpha = 0.8
                else:
                    # 较大值点的标签
                    xytext = (5, 15)
                    fontsize = 13
                    alpha = 0.9

                # 添加文本标签
                plt.annotate(label_text,
                           (x_val, y_val),
                           xytext=xytext,
                           textcoords='offset points',
                           fontsize=fontsize,
                           color=color,
                           alpha=alpha,
                           bbox=dict(boxstyle='round,pad=0.3',
                                   facecolor='white',
                                   edgecolor=color,
                                   alpha=0.8),
                           ha='center')

    # 绘制辅助线和时期标注
    if auxiliary_points['left_aux_idx'] is not None and auxiliary_points['right_aux_idx'] is not None:
        left_aux_date = auxiliary_points['left_aux_date']
        right_aux_date = auxiliary_points['right_aux_date']

        # 绘制辅助线
        plt.axvline(x=left_aux_date, color='red', linestyle='--', linewidth=2, alpha=0.7, label='左侧辅助线')
        plt.axvline(x=right_aux_date, color='blue', linestyle='--', linewidth=2, alpha=0.7, label='右侧辅助线')

        # 获取x轴范围和y轴最大值
        y_max = plt.ylim()[1]

        # 获取实际的日期范围
        date_range = plot_data['日期_日'].unique()
        date_range = sorted(date_range)
        x_min_date = date_range[0]
        x_max_date = date_range[-1]

        # 计算三个时期的中心位置（使用日期计算）
        # 潜伏期：从最小日期到左辅助线
        latent_days = (left_aux_date - x_min_date).days
        latent_center = x_min_date + pd.Timedelta(days=latent_days // 2)

        # 爆发期：从左辅助线到右辅助线
        outbreak_days = (right_aux_date - left_aux_date).days
        outbreak_center = left_aux_date + pd.Timedelta(days=outbreak_days // 2)

        # 衰退期：从右辅助线到最大日期
        decline_days = (x_max_date - right_aux_date).days
        decline_center = right_aux_date + pd.Timedelta(days=decline_days // 2)

        # 添加时期背景色块
        plt.axvspan(x_min_date, left_aux_date, alpha=0.1, color='green', label='潜伏期')
        plt.axvspan(left_aux_date, right_aux_date, alpha=0.1, color='orange', label='爆发期')
        plt.axvspan(right_aux_date, x_max_date, alpha=0.1, color='purple', label='衰退期')

        # 添加时期标注
        plt.text(latent_center, y_max * 0.95, '潜伏期',
                ha='center', va='top', fontsize=16, color='green', weight='bold',
                bbox=dict(boxstyle='round,pad=0.5', facecolor='white', edgecolor='green', alpha=0.9))

        plt.text(outbreak_center, y_max * 0.95, '爆发期',
                ha='center', va='top', fontsize=16, color='orange', weight='bold',
                bbox=dict(boxstyle='round,pad=0.5', facecolor='white', edgecolor='orange', alpha=0.9))

        plt.text(decline_center, y_max * 0.95, '衰退期',
                ha='center', va='top', fontsize=16, color='purple', weight='bold',
                bbox=dict(boxstyle='round,pad=0.5', facecolor='white', edgecolor='purple', alpha=0.9))

        # 在辅助线上添加日期标注
        plt.text(left_aux_date, y_max * 0.85, f'左辅助线\n{left_aux_date.strftime("%m-%d")}',
                ha='center', va='top', fontsize=11, color='red', weight='bold',
                bbox=dict(boxstyle='round,pad=0.3', facecolor='white', edgecolor='red', alpha=0.8))

        plt.text(right_aux_date, y_max * 0.85, f'右辅助线\n{right_aux_date.strftime("%m-%d")}',
                ha='center', va='top', fontsize=11, color='blue', weight='bold',
                bbox=dict(boxstyle='round,pad=0.3', facecolor='white', edgecolor='blue', alpha=0.8))

    # 设置图表属性
    plt.title('各类用户随时间变化趋势', fontsize=16, fontweight='bold')
    plt.xlabel('日期', fontsize=12)
    plt.ylabel('用户数量（条）', fontsize=12)
    plt.legend(loc='upper right', fontsize=10)
    plt.grid(True, alpha=0.3)
    plt.xticks(rotation=45)

    # 自动调整布局
    plt.tight_layout()

    # 保存图片
    plt.savefig('1_划分时间图.png', dpi=300, bbox_inches='tight')

    # 关闭图表以释放内存
    plt.close()

    # 打印统计信息
    print(f"\n统计信息:")
    optimized_date_range = plot_data['日期_日'].unique()
    print(f"优化后时间范围: {min(optimized_date_range)} 至 {max(optimized_date_range)}")
    print(f"优化后总天数: {len(optimized_date_range)}")

    for category in user_categories:
        total_count = valid_data[valid_data['用户划分'] == category].shape[0]
        print(f"{category}: 总计 {total_count} 条")

    # 返回辅助线点信息，供堆叠柱状图使用
    return auxiliary_points

def main():
    """主函数"""

    filename = '1.csv'
    df = pd.read_csv(filename, encoding='gb18030')

    # 处理日期
    df_processed = process_date_column(df)
    print(f"处理后数据形状: {df_processed.shape}")

    # 创建图表并获取辅助线点信息
    auxiliary_points = create_user_category_timeline(df_processed)

    # 创建堆叠柱状图
    if auxiliary_points:
        create_stacked_bar_chart(df_processed, auxiliary_points)

if __name__ == "__main__":
    main()
